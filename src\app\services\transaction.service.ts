import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';

// Transaction interfaces based on Prisma schema
export const TransactionType = {
    CLIENT_TO_DRIVER: 'CLIENT_TO_DRIVER',
    DRIVER_TO_COMPANY: 'DRIVER_TO_COMPANY',
} as const;

export type TransactionType =
    (typeof TransactionType)[keyof typeof TransactionType];

export const TransactionStatus = {
    PENDING: 'PENDING',
    COMPLETED: 'COMPLETED',
    FAILED: 'FAILED',
} as const;

export type TransactionStatus =
    (typeof TransactionStatus)[keyof typeof TransactionStatus];

export interface MoneyTransaction {
    id: string;
    type: TransactionType;
    status: TransactionStatus;
    amount: number; // Amount in USD

    // Order reference
    orderId: string;

    // Participants
    fromUserId?: string; // Null for company transactions
    toUserId?: string; // Null for company transactions

    // Metadata
    description?: string;
    createdAt: string;
    completedAt?: string;

    // Relations (if needed for display)
    order?: {
        id: string;
        // Add other order fields as needed
    };
    fromUser?: {
        id: string;
        firstName: string;
        lastName: string;
        phoneNumber: string;
    };
    toUser?: {
        id: string;
        firstName: string;
        lastName: string;
        phoneNumber: string;
    };
}

@Injectable({
    providedIn: 'root',
})
export class TransactionService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Get all transactions for the current user
     * @returns Observable with array of MoneyTransaction objects
     */
    getMyTransactions(): Observable<
        { data: MoneyTransaction[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/transactions/me',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<MoneyTransaction[]>(options);
    }

    // NOTE: The following methods were removed because they are not implemented in the backend:
    // - getDriverTransactions(driverId: string)
    // - markDriverTransactionsAsCompleted(driverId: string)
    // The backend only supports getting transactions for the current user via GET /api/transactions/me
}
