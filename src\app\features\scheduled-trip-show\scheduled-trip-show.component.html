<div
    class="pointer-events-auto m-0 flex h-full max-h-[95dvh] flex-col overflow-auto bg-background-color-100 p-0"
>
    <div
        class="flex h-full min-h-0 flex-1 flex-col overflow-auto bg-background-color-100"
    >
        <!-- Step 1: Select Pickup Location -->
        @if (currentStep() === CollaborativeTripStep.SELECT_PICKUP) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-background-color-100"
                    >
                        <i class="pi pi-map-marker text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Select Pickup Location
                        </h3>
                        <p class="text-sm text-text-color-200">
                            Choose from marked places, stop points, or select on
                            map
                        </p>
                    </div>
                </div>

                <!-- Marked Places -->
                @if (markedPlaces().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <h4
                            class="mb-3 text-sm font-medium text-text-color-100"
                        >
                            Marked Places
                        </h4>
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                                    (click)="selectPickupMarkedPlace(place)"
                                >
                                    <span>{{ place.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Stop Points -->
                @if (stopPoints().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <h4
                            class="mb-3 text-sm font-medium text-text-color-100"
                        >
                            Stop Points
                        </h4>
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (
                                stopPoint of stopPoints();
                                track stopPoint.id
                            ) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-blue-600 hover:bg-blue-600 hover:text-background-color-100"
                                    (click)="selectPickupStopPoint(stopPoint)"
                                >
                                    <span>{{ stopPoint.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Map Location Picker -->
                <div class="flex h-auto min-h-0 flex-1 flex-col">
                    <app-location-picker
                        title="Or select on map"
                        placeholder="Enter pickup location"
                        (locationSelected)="onLocationSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }

        <!-- Step 2: Select Dropoff Location -->
        @if (currentStep() === CollaborativeTripStep.SELECT_DROPOFF) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <button
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-background-color-300 text-text-color-100 hover:bg-background-color-400"
                        (click)="goBack()"
                    >
                        <i class="pi pi-arrow-left"></i>
                    </button>
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-blue-600 text-background-color-100"
                    >
                        <i class="pi pi-flag text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Select Dropoff Location
                        </h3>
                        <p class="text-sm text-text-color-200">
                            Pickup: {{ pickupAddress() }}
                        </p>
                    </div>
                </div>

                <!-- Marked Places -->
                @if (markedPlaces().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <h4
                            class="mb-3 text-sm font-medium text-text-color-100"
                        >
                            Marked Places
                        </h4>
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-main-color-600 hover:bg-main-color-600 hover:text-background-color-100"
                                    (click)="selectDropoffMarkedPlace(place)"
                                >
                                    <span>{{ place.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Stop Points -->
                @if (stopPoints().length > 0) {
                    <div class="border-b border-background-color-300 p-4">
                        <h4
                            class="mb-3 text-sm font-medium text-text-color-100"
                        >
                            Stop Points
                        </h4>
                        <div class="flex gap-3 overflow-x-auto pb-2">
                            @for (
                                stopPoint of stopPoints();
                                track stopPoint.id
                            ) {
                                <div
                                    class="flex-shrink-0 cursor-pointer rounded-full border border-background-color-300 bg-background-color-100 px-4 py-2 text-sm font-medium text-text-color-100 transition-all duration-200 hover:border-blue-600 hover:bg-blue-600 hover:text-background-color-100"
                                    (click)="selectDropoffStopPoint(stopPoint)"
                                >
                                    <span>{{ stopPoint.name }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Map Location Picker -->
                <div class="flex h-auto min-h-0 flex-1 flex-col">
                    <app-location-picker
                        title="Or select on map"
                        placeholder="Enter dropoff location"
                        (locationSelected)="onLocationSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }

        <!-- Step 3: Set Number of Passengers -->
        @if (currentStep() === CollaborativeTripStep.SET_PASSENGERS) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <button
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-background-color-300 text-text-color-100 hover:bg-background-color-400"
                        (click)="goBack()"
                    >
                        <i class="pi pi-arrow-left"></i>
                    </button>
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-purple-600 text-background-color-100"
                    >
                        <i class="pi pi-users text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Set Passengers & Notes
                        </h3>
                        <p class="text-sm text-text-color-200">
                            From: {{ pickupAddress() }} → {{ dropoffAddress() }}
                        </p>
                    </div>
                </div>

                <div class="flex-1 p-6">
                    <div class="mx-auto max-w-md space-y-6">
                        <!-- Number of Passengers -->
                        <div>
                            <label
                                class="mb-2 block text-sm font-medium text-text-color-100"
                            >
                                Number of Passengers
                            </label>
                            <p-inputNumber
                                [(ngModel)]="numberOfPassengers"
                                [min]="1"
                                [max]="8"
                                [showButtons]="true"
                                buttonLayout="horizontal"
                                spinnerMode="horizontal"
                                decrementButtonClass="p-button-secondary"
                                incrementButtonClass="p-button-secondary"
                                class="w-full"
                            >
                            </p-inputNumber>
                        </div>

                        <!-- Passenger Notes -->
                        <div>
                            <label
                                class="mb-2 block text-sm font-medium text-text-color-100"
                            >
                                Notes (Optional)
                            </label>
                            <textarea
                                [(ngModel)]="passengerNotes"
                                placeholder="Any special requests or notes for the driver..."
                                class="w-full rounded-lg border border-background-color-300 bg-background-color-100 p-3 text-text-color-100 focus:border-main-color-600 focus:outline-none focus:ring-2 focus:ring-main-color-600/20"
                                rows="4"
                            ></textarea>
                        </div>

                        <!-- Search Button -->
                        <button
                            [disabled]="!canSearchTrips() || isLoading()"
                            (click)="searchMatchingTrips()"
                            class="w-full rounded-lg bg-main-color-600 px-6 py-3 font-medium text-background-color-100 transition-colors hover:bg-main-color-700 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            @if (isLoading()) {
                                <i class="pi pi-spin pi-spinner mr-2"></i>
                                Searching...
                            } @else {
                                <i class="pi pi-search mr-2"></i>
                                Find Matching Trips
                            }
                        </button>
                    </div>
                </div>
            </div>
        }

        <!-- Step 4: Show Results -->
        @if (currentStep() === CollaborativeTripStep.SHOW_RESULTS) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <button
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-background-color-300 text-text-color-100 hover:bg-background-color-400"
                        (click)="goBack()"
                    >
                        <i class="pi pi-arrow-left"></i>
                    </button>
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-orange-600 text-background-color-100"
                    >
                        <i class="pi pi-list text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Available Trips
                        </h3>
                        <p class="text-sm text-text-color-200">
                            {{ availableTrips().length }} trip(s) found for
                            {{ numberOfPassengers() }} passenger(s)
                        </p>
                    </div>
                </div>

                <div class="flex-1 overflow-auto p-4">
                    @if (availableTrips().length === 0) {
                        <div
                            class="flex flex-col items-center justify-center py-12 text-center"
                        >
                            <i
                                class="pi pi-search mb-4 text-4xl text-text-color-300"
                            ></i>
                            <h3
                                class="mb-2 text-lg font-medium text-text-color-100"
                            >
                                No Trips Found
                            </h3>
                            <p class="text-text-color-200">
                                No collaborative trips match your criteria. Try
                                adjusting your locations or passenger count.
                            </p>
                            <button
                                (click)="goBack()"
                                class="mt-4 rounded-lg bg-main-color-600 px-6 py-2 text-background-color-100 hover:bg-main-color-700"
                            >
                                Try Again
                            </button>
                        </div>
                    } @else {
                        <div class="space-y-4">
                            @for (trip of availableTrips(); track trip.id) {
                                <div
                                    class="rounded-lg border border-background-color-300 bg-background-color-100 p-4 shadow-sm"
                                >
                                    <!-- Trip Header -->
                                    <div
                                        class="mb-3 flex items-start justify-between"
                                    >
                                        <div class="flex-1">
                                            <div
                                                class="flex items-center gap-2 mb-1"
                                            >
                                                <i
                                                    class="pi pi-user text-main-color-600"
                                                ></i>
                                                <span
                                                    class="font-medium text-text-color-100"
                                                >
                                                    {{ trip.driver?.firstName }}
                                                    {{ trip.driver?.lastName }}
                                                </span>
                                            </div>
                                            <div
                                                class="flex items-center gap-2 text-sm text-text-color-200"
                                            >
                                                <i class="pi pi-clock"></i>
                                                <span>{{
                                                    formatDate(
                                                        trip.scheduledTime
                                                    )
                                                }}</span>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div
                                                class="text-lg font-bold text-main-color-600"
                                            >
                                                ${{
                                                    trip.pricePerSeat *
                                                        numberOfPassengers()
                                                }}
                                            </div>
                                            <div
                                                class="text-sm text-text-color-200"
                                            >
                                                ${{ trip.pricePerSeat }}/seat
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Trip Details -->
                                    <div class="mb-3 space-y-2">
                                        <div
                                            class="flex items-center gap-2 text-sm"
                                        >
                                            <i
                                                class="pi pi-users text-blue-600"
                                            ></i>
                                            <span class="text-text-color-100">
                                                {{ getAvailableSeats(trip) }}
                                                seats available
                                            </span>
                                        </div>
                                        @if (trip.driverNotes) {
                                            <div
                                                class="flex items-start gap-2 text-sm"
                                            >
                                                <i
                                                    class="pi pi-comment text-gray-600 mt-0.5"
                                                ></i>
                                                <span
                                                    class="text-text-color-200"
                                                    >{{
                                                        trip.driverNotes
                                                    }}</span
                                                >
                                            </div>
                                        }
                                    </div>

                                    <!-- Book Button -->
                                    <button
                                        [disabled]="
                                            !hasEnoughSeats(trip) || isLoading()
                                        "
                                        (click)="bookTrip(trip)"
                                        class="w-full rounded-lg bg-green-600 px-4 py-2 font-medium text-background-color-100 transition-colors hover:bg-green-700 disabled:cursor-not-allowed disabled:bg-gray-400"
                                    >
                                        @if (!hasEnoughSeats(trip)) {
                                            <i class="pi pi-times mr-2"></i>
                                            Not Enough Seats
                                        } @else if (
                                            isLoading() &&
                                            selectedTrip()?.id === trip.id
                                        ) {
                                            <i
                                                class="pi pi-spin pi-spinner mr-2"
                                            ></i>
                                            Booking...
                                        } @else {
                                            <i class="pi pi-check mr-2"></i>
                                            Book This Trip
                                        }
                                    </button>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Step 5: Booking Confirmation -->
        @if (currentStep() === CollaborativeTripStep.BOOKING_CONFIRMATION) {
            <div class="flex flex-1 flex-col bg-background-color-100">
                <div
                    class="flex items-center gap-4 border-b border-background-color-300 p-6 ps-8"
                >
                    <div
                        class="flex h-12 w-12 items-center justify-center rounded-full bg-green-600 text-background-color-100"
                    >
                        <i class="pi pi-check text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-text-color-100">
                            Booking Confirmed!
                        </h3>
                        <p class="text-sm text-text-color-200">
                            Your trip has been successfully booked
                        </p>
                    </div>
                </div>

                <div class="flex-1 p-6">
                    @if (bookingResult()) {
                        <div class="mx-auto max-w-md space-y-6">
                            <!-- Success Message -->
                            <div
                                class="rounded-lg bg-green-50 border border-green-200 p-4 text-center"
                            >
                                <i
                                    class="pi pi-check-circle text-3xl text-green-600 mb-2"
                                ></i>
                                <h3
                                    class="text-lg font-medium text-green-800 mb-1"
                                >
                                    Trip Booked Successfully!
                                </h3>
                                <p class="text-sm text-green-700">
                                    You will receive a confirmation notification
                                    shortly.
                                </p>
                            </div>

                            <!-- Booking Details -->
                            <div
                                class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                            >
                                <h4
                                    class="mb-3 font-medium text-text-color-100"
                                >
                                    Booking Details
                                </h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-text-color-200"
                                            >Booking ID:</span
                                        >
                                        <span
                                            class="font-mono text-text-color-100"
                                            >{{ bookingResult()!.id }}</span
                                        >
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-color-200"
                                            >Passengers:</span
                                        >
                                        <span class="text-text-color-100">{{
                                            bookingResult()!.numberOfPassengers
                                        }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-color-200"
                                            >Total Cost:</span
                                        >
                                        <span
                                            class="font-medium text-main-color-600"
                                            >${{
                                                bookingResult()!.totalCost
                                            }}</span
                                        >
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-color-200"
                                            >Booked At:</span
                                        >
                                        <span class="text-text-color-100">{{
                                            formatDate(
                                                bookingResult()!.createdAt
                                            )
                                        }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Trip Details -->
                            @if (selectedTrip()) {
                                <div
                                    class="rounded-lg border border-background-color-300 bg-background-color-100 p-4"
                                >
                                    <h4
                                        class="mb-3 font-medium text-text-color-100"
                                    >
                                        Trip Details
                                    </h4>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span class="text-text-color-200"
                                                >Driver:</span
                                            >
                                            <span class="text-text-color-100">
                                                {{
                                                    selectedTrip()!.driver
                                                        ?.firstName
                                                }}
                                                {{
                                                    selectedTrip()!.driver
                                                        ?.lastName
                                                }}
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-text-color-200"
                                                >Scheduled Time:</span
                                            >
                                            <span class="text-text-color-100">{{
                                                formatDate(
                                                    selectedTrip()!
                                                        .scheduledTime
                                                )
                                            }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-text-color-200"
                                                >From:</span
                                            >
                                            <span class="text-text-color-100">{{
                                                pickupAddress()
                                            }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-text-color-200"
                                                >To:</span
                                            >
                                            <span class="text-text-color-100">{{
                                                dropoffAddress()
                                            }}</span>
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- Action Buttons -->
                            <div class="space-y-3">
                                <button
                                    (click)="goToMain()"
                                    class="w-full rounded-lg bg-main-color-600 px-6 py-3 font-medium text-background-color-100 transition-colors hover:bg-main-color-700"
                                >
                                    <i class="pi pi-home mr-2"></i>
                                    Go to Main Page
                                </button>
                                <button
                                    (click)="startOver()"
                                    class="w-full rounded-lg border border-background-color-300 bg-background-color-100 px-6 py-3 font-medium text-text-color-100 transition-colors hover:bg-background-color-200"
                                >
                                    <i class="pi pi-plus mr-2"></i>
                                    Book Another Trip
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</div>
