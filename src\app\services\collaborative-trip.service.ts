import { DestroyRef, Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import {
    BookOfferDto,
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
    FindMatchingTripsDto,
} from '../core/types/tripoos.types';
import { HttpService, requestOptions } from './http.service';

// Re-export types for convenience
export type {
    BookOfferDto,
    CollaborativeTripBooking,
    CollaborativeTripOffer,
    CreateCollaborativeTripOfferDto,
    FindMatchingTripsDto,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class CollaborativeTripService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Create a new collaborative trip offer (driver only)
     * @param createDto Collaborative trip offer creation data
     * @returns Observable with created CollaborativeTripOffer data
     */
    createCollaborativeTripOffer(
        createDto: CreateCollaborativeTripOfferDto,
    ): Observable<
        | { data: CollaborativeTripOffer; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips',
            opj: createDto,
            des: this.destroyRef,
            successMessage: 'Collaborative trip offer created successfully',
            failedMessage: 'Failed to create collaborative trip offer',
        };
        return this.http.post<CollaborativeTripOffer>(options);
    }

    /**
     * Get recent collaborative trip offers for the current driver
     * Recent offers are those scheduled within the next 24 hours
     * @returns Observable with array of recent CollaborativeTripOffer objects
     */
    getMyRecentCollaborativeTripOffers(): Observable<
        | { data: CollaborativeTripOffer[]; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/my/recent',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }

    /**
     * Get old collaborative trip offers for the current driver
     * Old offers are those scheduled more than 24 hours from now or in the past
     * @returns Observable with array of old CollaborativeTripOffer objects
     */
    getMyOldCollaborativeTripOffers(): Observable<
        | { data: CollaborativeTripOffer[]; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/my/old',
            des: this.destroyRef,
            successToast: false, // Don't show success toast for data fetching
        };
        return this.http.get<CollaborativeTripOffer[]>(options);
    }

    /**
     * Find matching collaborative trip offers based on search criteria
     * @param searchDto Search criteria for finding matching trips
     * @returns Observable with array of matching CollaborativeTripOffer objects
     */
    findMatchingCollaborativeTripOffers(
        searchDto: FindMatchingTripsDto,
    ): Observable<
        | { data: CollaborativeTripOffer[]; error: null }
        | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/collaborative-trips/find-matching',
            opj: searchDto,
            des: this.destroyRef,
            successToast: false, // Don't show success toast for search results
        };
        return this.http.post<CollaborativeTripOffer[]>(options);
    }

    /**
     * Book a collaborative trip offer (passenger)
     * @param offerId ID of the collaborative trip offer to book
     * @param bookDto Booking details
     * @returns Observable with booking confirmation
     */
    bookCollaborativeTripOffer(
        offerId: string,
        bookDto: BookOfferDto,
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/collaborative-trips/book-offer/${offerId}`,
            opj: bookDto,
            des: this.destroyRef,
            successMessage: 'Successfully booked collaborative trip',
            failedMessage: 'Failed to book collaborative trip',
        };
        return this.http.post<any>(options);
    }
}
